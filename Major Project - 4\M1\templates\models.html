<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DARU-Net: Model Performance</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.98);
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            font-weight: 700;
        }

        .header p {
            font-size: 1.3em;
            opacity: 0.95;
            font-weight: 300;
            margin-bottom: 30px;
        }

        .nav-menu {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
        }

        .nav-item {
            color: white;
            text-decoration: none;
            padding: 12px 25px;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 1.1em;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .nav-item.active {
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .main-content {
            padding: 50px;
        }

        .performance-section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 2.5em;
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: center;
            font-weight: 700;
        }

        .metrics-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 50px;
        }

        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
            transition: all 0.4s ease;
        }

        .metric-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 50px rgba(102, 126, 234, 0.6);
        }

        .metric-card h3 {
            font-size: 1.2em;
            margin-bottom: 15px;
            opacity: 0.95;
            font-weight: 600;
        }

        .metric-card p {
            font-size: 2.5em;
            font-weight: 900;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-bottom: 50px;
        }

        @media (max-width: 1200px) {
            .charts-grid {
                grid-template-columns: 1fr 1fr;
            }
        }

        .chart-container {
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .chart-container h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
            font-weight: 600;
        }

        .chart-placeholder {
            width: 100%;
            height: 300px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 1.2em;
            border: 2px dashed #dee2e6;
        }

        .chart-container img {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            background: white;
            padding: 5px;
            cursor: pointer;
        }

        .chart-container img:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .model-comparison {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 40px;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
        }

        .comparison-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
        }

        .comparison-table tr:hover {
            background: #f8f9fa;
        }

        .best-model {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
            color: white;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-menu {
                flex-direction: column;
                gap: 15px;
                align-items: center;
            }
            
            .nav-item {
                width: 200px;
                text-align: center;
            }
            
            .main-content {
                padding: 30px;
            }
        }
        /* Modal for full-size image viewing */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            animation: fadeIn 0.3s ease;
        }

        .modal-content {
            margin: auto;
            display: block;
            max-width: 90%;
            max-height: 90%;
            margin-top: 5%;
            border-radius: 10px;
        }

        .close {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #bbb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>DARU-Net</h1>
            <p>Model Performance & Training Results</p>
            
            <!-- Navigation Menu -->
            <nav class="nav-menu">
                <a href="/" class="nav-item">🏠 Home</a>
                <a href="/analysis" class="nav-item">🔬 Analysis</a>
                <a href="/models" class="nav-item active">📊 Performance</a>
                <a href="/chatbot" class="nav-item">💬 Assistant</a>
            </nav>
        </div>

        <div class="main-content">
            <!-- Performance Overview -->
            <div class="performance-section">
                <h2 class="section-title">📈 Training Results</h2>
                
                <div class="metrics-overview">
                    <div class="metric-card">
                        <h3>🎯 Best Accuracy</h3>
                        <p>92.81%</p>
                    </div>
                    <div class="metric-card">
                        <h3>📊 F1-Score</h3>
                        <p>0.9273</p>
                    </div>
                    <div class="metric-card">
                        <h3>🎯 Precision</h3>
                        <p>0.9156</p>
                    </div>
                    <div class="metric-card">
                        <h3>📈 Recall</h3>
                        <p>0.9394</p>
                    </div>
                    <div class="metric-card">
                        <h3>⚡ Training Time</h3>
                        <p>45 min</p>
                    </div>
                    <div class="metric-card">
                        <h3>🔧 Parameters</h3>
                        <p>2.1M</p>
                    </div>
                </div>
            </div>

            <!-- Training Charts -->
            <div class="performance-section">
                <h2 class="section-title">📊 Training Progress & Results</h2>
                <p style="text-align: center; color: #6c757d; margin-bottom: 30px; font-size: 1.1em;">
                    Real training graphs from the DARU-Net CPU-optimized model achieving 92.81% accuracy
                </p>
                
                <div class="charts-grid">
                    <div class="chart-container">
                        <h3>📈 Training Progress</h3>
                        <img src="/static/results/training_progress_latest.png" alt="Training Progress">
                    </div>
                    <div class="chart-container">
                        <h3>📊 Accuracy & F1-Score Combined</h3>
                        <img src="/static/results/accuracy_f1_combined_latest.png" alt="Accuracy F1 Combined">
                    </div>
                    <div class="chart-container">
                        <h3>🎯 Accuracy Line Graph</h3>
                        <img src="/static/results/accuracy_line_graph_latest.png" alt="Accuracy Line Graph">
                    </div>
                    <div class="chart-container">
                        <h3>📈 F1-Score Progress</h3>
                        <img src="/static/results/f1_score_line_graph_latest.png" alt="F1 Score Line Graph">
                    </div>
                    <div class="chart-container">
                        <h3>📊 Metrics Bar Chart</h3>
                        <img src="/static/results/metrics_bar_graph_latest.png" alt="Metrics Bar Graph">
                    </div>
                    <div class="chart-container">
                        <h3>🎯 Accuracy & Precision Bar</h3>
                        <img src="/static/results/accuracy_precision_bar_latest.png" alt="Accuracy Precision Bar">
                    </div>
                </div>
            </div>

            <!-- Model Comparison -->
            <div class="model-comparison">
                <h2 class="section-title">🏆 Model Comparison</h2>
                
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>Model</th>
                            <th>Accuracy (%)</th>
                            <th>F1-Score</th>
                            <th>Precision</th>
                            <th>Recall</th>
                            <th>Parameters</th>
                            <th>Training Time</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="best-model">
                            <td><strong>DARU-Net (CPU Optimized)</strong></td>
                            <td><strong>92.81</strong></td>
                            <td><strong>0.9273</strong></td>
                            <td><strong>0.9156</strong></td>
                            <td><strong>0.9394</strong></td>
                            <td><strong>2.1M</strong></td>
                            <td><strong>45 min</strong></td>
                        </tr>
                        <tr>
                            <td>DARU-Net (Original)</td>
                            <td>91.45</td>
                            <td>0.9187</td>
                            <td>0.9089</td>
                            <td>0.9287</td>
                            <td>3.2M</td>
                            <td>78 min</td>
                        </tr>
                        <tr>
                            <td>U-Net Baseline</td>
                            <td>87.23</td>
                            <td>0.8756</td>
                            <td>0.8634</td>
                            <td>0.8881</td>
                            <td>7.8M</td>
                            <td>92 min</td>
                        </tr>
                        <tr>
                            <td>ResNet-50 + FCN</td>
                            <td>84.67</td>
                            <td>0.8421</td>
                            <td>0.8298</td>
                            <td>0.8547</td>
                            <td>25.6M</td>
                            <td>156 min</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Modal for full-size image viewing -->
    <div id="imageModal" class="modal">
        <span class="close">&times;</span>
        <img class="modal-content" id="modalImage">
    </div>

    <script>
        // Get modal elements
        const modal = document.getElementById('imageModal');
        const modalImg = document.getElementById('modalImage');
        const closeBtn = document.getElementsByClassName('close')[0];

        // Add click event to all chart images
        document.querySelectorAll('.chart-container img').forEach(img => {
            img.addEventListener('click', function() {
                modal.style.display = 'block';
                modalImg.src = this.src;
            });
        });

        // Close modal when clicking the X
        closeBtn.addEventListener('click', function() {
            modal.style.display = 'none';
        });

        // Close modal when clicking outside the image
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                modal.style.display = 'none';
            }
        });
    </script>
</body>
</html>
