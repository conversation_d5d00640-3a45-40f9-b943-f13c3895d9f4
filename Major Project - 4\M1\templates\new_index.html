<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DARU-Net: Burned Area Detection</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background:
                radial-gradient(ellipse at top, #1e3c72 0%, #2a5298 50%, #0f1419 100%),
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
            background-size: 100% 100%, 50% 50%, 80% 80%, 100% 50%;
            background-attachment: fixed;
            min-height: 100vh;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
        }

        /* Animated stars background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(2px 2px at 20px 30px, #eee, transparent),
                radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
                radial-gradient(1px 1px at 90px 40px, #fff, transparent),
                radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
                radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
            background-repeat: repeat;
            background-size: 200px 100px;
            animation: sparkle 20s linear infinite;
            pointer-events: none;
            z-index: 1;
        }

        /* Floating satellite animation */
        body::after {
            content: '🛰️';
            position: fixed;
            font-size: 30px;
            top: 20%;
            right: -50px;
            animation: satellite-float 25s linear infinite;
            pointer-events: none;
            z-index: 2;
            filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
        }

        @keyframes sparkle {
            from { transform: translateX(0); }
            to { transform: translateX(-200px); }
        }

        @keyframes satellite-float {
            0% {
                right: -50px;
                top: 20%;
                transform: rotate(0deg);
            }
            25% {
                right: 25%;
                top: 15%;
                transform: rotate(90deg);
            }
            50% {
                right: 50%;
                top: 25%;
                transform: rotate(180deg);
            }
            75% {
                right: 75%;
                top: 10%;
                transform: rotate(270deg);
            }
            100% {
                right: 100%;
                top: 20%;
                transform: rotate(360deg);
            }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            overflow: hidden;
            position: relative;
            z-index: 10;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Add some cosmic particles around the container */
        .container::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg,
                rgba(255, 0, 150, 0.3) 0%,
                rgba(0, 204, 255, 0.3) 25%,
                rgba(255, 0, 150, 0.3) 50%,
                rgba(0, 204, 255, 0.3) 75%,
                rgba(255, 0, 150, 0.3) 100%);
            border-radius: 22px;
            z-index: -1;
            animation: cosmic-glow 4s ease-in-out infinite;
        }

        @keyframes cosmic-glow {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 0.8; }
        }

        .header {
            background:
                linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1e3c72 100%),
                radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(0, 204, 255, 0.2) 0%, transparent 50%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        /* Add floating cosmic elements to header */
        .header::before {
            content: '✨ 🌟 ⭐ 🛸 🌌 ✨ 🌟 ⭐';
            position: absolute;
            top: 10px;
            left: -100%;
            width: 200%;
            font-size: 20px;
            animation: cosmic-float 30s linear infinite;
            opacity: 0.6;
            pointer-events: none;
        }

        @keyframes cosmic-float {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            font-weight: 700;
        }

        .header p {
            font-size: 1.3em;
            opacity: 0.95;
            font-weight: 300;
            margin-bottom: 30px;
        }

        .nav-menu {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
        }

        .nav-item {
            color: white;
            text-decoration: none;
            padding: 12px 25px;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 1.1em;
            position: relative;
            z-index: 100;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .nav-item.active {
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 768px) {
            .nav-menu {
                flex-direction: column;
                gap: 15px;
                align-items: center;
            }

            .nav-item {
                width: 200px;
                text-align: center;
            }
        }

        .main-content {
            padding: 50px;
            position: relative;
            z-index: 20;
        }

        /* Add floating cosmic elements */
        .main-content::before {
            content: '🌟';
            position: absolute;
            top: 20px;
            left: 20px;
            font-size: 20px;
            animation: float-left 8s ease-in-out infinite;
            opacity: 0.6;
        }

        .main-content::after {
            content: '✨';
            position: absolute;
            top: 50px;
            right: 30px;
            font-size: 18px;
            animation: float-right 6s ease-in-out infinite reverse;
            opacity: 0.7;
        }

        @keyframes float-left {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(180deg); }
        }

        @keyframes float-right {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-15px) rotate(-180deg); }
        }

        .upload-section {
            background:
                linear-gradient(135deg, rgba(248, 249, 250, 0.95) 0%, rgba(233, 236, 239, 0.95) 100%),
                radial-gradient(circle at 20% 20%, rgba(0, 204, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255, 0, 150, 0.1) 0%, transparent 50%);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            border: 3px dashed rgba(0, 123, 255, 0.6);
            transition: all 0.4s ease;
            position: relative;
            backdrop-filter: blur(5px);
        }

        .upload-section::before {
            content: '🌌';
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 24px;
            animation: gentle-pulse 3s ease-in-out infinite;
        }

        @keyframes gentle-pulse {
            0%, 100% { transform: scale(1); opacity: 0.7; }
            50% { transform: scale(1.1); opacity: 1; }
        }

        .upload-section:hover {
            border-color: rgba(0, 86, 179, 0.8);
            background:
                linear-gradient(135deg, rgba(227, 242, 253, 0.95) 0%, rgba(187, 222, 251, 0.95) 100%),
                radial-gradient(circle at 20% 20%, rgba(0, 204, 255, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255, 0, 150, 0.2) 0%, transparent 50%);
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 123, 255, 0.3);
        }

        .upload-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .upload-item {
            text-align: center;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            z-index: 50;
        }

        .upload-item:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .upload-item h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.4em;
            font-weight: 600;
        }

        .file-input-wrapper {
            position: relative;
            display: inline-block;
            cursor: pointer;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 18px 35px;
            border-radius: 50px;
            transition: all 0.4s ease;
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
            font-weight: 600;
            font-size: 1.1em;
            z-index: 100;
        }

        .file-input-wrapper:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(79, 172, 254, 0.6);
            background: linear-gradient(135deg, #00f2fe 0%, #4facfe 100%);
        }

        .file-input-wrapper input[type="file"] {
            display: none;
        }

        .file-name {
            margin-top: 15px;
            font-size: 1em;
            color: #495057;
            min-height: 25px;
            font-weight: 500;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .analyze-btn {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
            border: none;
            padding: 20px 50px;
            font-size: 1.3em;
            font-weight: 700;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.4s ease;
            box-shadow: 0 8px 25px rgba(86, 171, 47, 0.4);
            display: block;
            margin: 0 auto;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            z-index: 100;
        }

        .analyze-btn:hover:not(:disabled) {
            transform: translateY(-4px);
            box-shadow: 0 15px 40px rgba(86, 171, 47, 0.6);
            background: linear-gradient(135deg, #a8e6cf 0%, #56ab2f 100%);
        }

        .analyze-btn:disabled {
            background: linear-gradient(135deg, #6c757d 0%, #adb5bd 100%);
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        }

        .loading {
            display: none;
            text-align: center;
            margin: 40px 0;
            padding: 30px;
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-radius: 15px;
            border: 2px solid #ffc107;
        }

        .spinner {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #007bff;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            animation: spin 1s linear infinite;
            margin: 0 auto 25px;
        }

        .loading-text {
            font-size: 1.2em;
            color: #856404;
            font-weight: 600;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .results-section {
            display: none;
            margin-top: 40px;
            animation: fadeInUp 0.6s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: 2fr 2fr 1fr 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
            align-items: stretch;
        }

        @media (max-width: 1200px) {
            .metrics-grid {
                grid-template-columns: 1fr 1fr 1fr;
                gap: 25px;
            }
        }

        @media (max-width: 768px) {
            .metrics-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }

        .metric-card {
            background:
                linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1e3c72 100%),
                radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            color: white;
            padding: 35px 25px;
            border-radius: 20px;
            text-align: center;
            box-shadow:
                0 10px 30px rgba(30, 60, 114, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.1);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        /* Add twinkling stars to metric cards */
        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(1px 1px at 20% 30%, rgba(255,255,255,0.8), transparent),
                radial-gradient(1px 1px at 40% 70%, rgba(255,255,255,0.6), transparent),
                radial-gradient(1px 1px at 90% 40%, rgba(255,255,255,0.9), transparent),
                radial-gradient(1px 1px at 70% 20%, rgba(255,255,255,0.7), transparent);
            background-size: 100% 100%;
            animation: twinkle 4s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes twinkle {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.8; }
        }

        .metric-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 50px rgba(102, 126, 234, 0.6);
        }

        .metric-card h3 {
            font-size: 1.2em;
            margin-bottom: 15px;
            opacity: 0.95;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .metric-card p {
            font-size: 2.5em;
            font-weight: 900;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .accuracy-highlight {
            background: linear-gradient(135deg, #00d2ff 0%, #3a7bd5 100%) !important;
            position: relative;
            overflow: hidden;
            border: 3px solid #ffffff;
            box-shadow: 0 15px 40px rgba(0, 210, 255, 0.4) !important;
            animation: accuracyGlow 3s ease-in-out infinite;
        }

        .accuracy-highlight::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transform: rotate(45deg);
            animation: shine 2s infinite;
        }

        .accuracy-highlight .metric-card h3 {
            font-size: 1.4em !important;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            margin-bottom: 20px !important;
        }

        .accuracy-highlight .metric-card p {
            font-size: 3.5em !important;
            font-weight: 900 !important;
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.5);
            position: relative;
            z-index: 2;
            margin: 0;
            line-height: 1;
        }

        @keyframes accuracyGlow {
            0%, 100% {
                box-shadow: 0 15px 40px rgba(0, 210, 255, 0.4);
                transform: translateY(-8px) scale(1);
            }
            50% {
                box-shadow: 0 25px 60px rgba(0, 210, 255, 0.8);
                transform: translateY(-12px) scale(1.02);
            }
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .accuracy-highlight:hover {
            transform: translateY(-15px) scale(1.05) !important;
            box-shadow: 0 30px 80px rgba(0, 210, 255, 1) !important;
        }

        .images-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-top: 40px;
        }

        .image-container {
            text-align: center;
            background: white;
            padding: 25px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.4s ease;
        }

        .image-container:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
        }

        .image-container h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.4em;
            font-weight: 600;
        }

        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.4s ease;
        }

        .image-container img:hover {
            transform: scale(1.05);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.25);
        }

        .error-message {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
            padding: 20px;
            border-radius: 15px;
            margin: 25px 0;
            border: 2px solid #f5c6cb;
            display: none;
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(114, 28, 36, 0.2);
        }

        @media (max-width: 768px) {
            .upload-grid,
            .images-grid {
                grid-template-columns: 1fr;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2.2em;
            }
            
            .main-content {
                padding: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>DARU-Net</h1>
            <p>Advanced Burned Area Detection using Sentinel-1 & Sentinel-2 Imagery</p>

            <!-- Navigation Menu -->
            <nav class="nav-menu">
                <a href="/" class="nav-item">🏠 Home</a>
                <a href="/analysis" class="nav-item active">🔬 Analysis</a>
                <a href="/models" class="nav-item">📊 Performance</a>
                <a href="/chatbot" class="nav-item">💬 Assistant</a>
            </nav>
        </div>

        <div class="main-content">
            <div class="upload-section">
                <div class="upload-grid">
                    <div class="upload-item">
                        <h3>📡 Sentinel-1 Image (FIRST)</h3>
                        <div class="file-input-wrapper" onclick="document.getElementById('s1Image').click()">
                            <input type="file" id="s1Image" accept="image/*" style="display: none;">
                            <span>Choose Sentinel-1 Image</span>
                        </div>
                        <div class="file-name" id="s1FileName">No file selected</div>
                    </div>
                    <div class="upload-item">
                        <h3>🛰️ Sentinel-2 Image (SECOND)</h3>
                        <div class="file-input-wrapper" onclick="document.getElementById('s2Image').click()">
                            <input type="file" id="s2Image" accept="image/*" style="display: none;">
                            <span>Choose Sentinel-2 Image</span>
                        </div>
                        <div class="file-name" id="s2FileName">No file selected</div>
                    </div>
                </div>
                <button class="analyze-btn" onclick="analyzeImages()" id="analyzeBtn" disabled>
                    🚀 Analyze Images
                </button>
            </div>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <div class="loading-text">🔍 Processing images with DARU-Net...</div>
            </div>

            <div class="error-message" id="errorMessage"></div>

            <div class="results-section" id="results">
                <div class="metrics-grid">
                    <div class="metric-card accuracy-highlight">
                        <h3>🏆 MODEL ACCURACY</h3>
                        <p id="accuracyValue">--</p>
                        <div style="font-size: 0.9em; margin-top: 10px; opacity: 0.9; font-weight: 500;">
                            Burned Area Detection
                        </div>
                    </div>
                    <div class="metric-card" style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;">
                        <h3>🔥 BURNED AREA</h3>
                        <p id="burnedAreaValue">--</p>
                        <div style="font-size: 0.9em; margin-top: 10px; opacity: 0.9; font-weight: 500;">
                            Square Kilometers
                        </div>
                    </div>
                    <div class="metric-card">
                        <h3>📊 F1-Score</h3>
                        <p id="f1Value">--</p>
                    </div>
                    <div class="metric-card">
                        <h3>🎯 Precision</h3>
                        <p id="precisionValue">--</p>
                    </div>
                    <div class="metric-card">
                        <h3>📈 Recall</h3>
                        <p id="recallValue">--</p>
                    </div>
                </div>

                <div class="images-grid">
                    <div class="image-container">
                        <h3>🔥 Predicted Burned Areas</h3>
                        <img id="predictionImage" src="" alt="Prediction">
                    </div>
                    <div class="image-container">
                        <h3>📊 Performance Plot</h3>
                        <img id="performancePlot" src="" alt="Performance">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // File input handlers
        document.getElementById('s1Image').addEventListener('change', function(e) {
            const fileName = e.target.files[0] ? e.target.files[0].name : 'No file selected';
            document.getElementById('s1FileName').textContent = fileName;
            checkFilesSelected();
        });

        document.getElementById('s2Image').addEventListener('change', function(e) {
            const fileName = e.target.files[0] ? e.target.files[0].name : 'No file selected';
            document.getElementById('s2FileName').textContent = fileName;
            checkFilesSelected();
        });

        function checkFilesSelected() {
            const s1File = document.getElementById('s1Image').files[0];
            const s2File = document.getElementById('s2Image').files[0];
            const analyzeBtn = document.getElementById('analyzeBtn');
            
            if (s1File && s2File) {
                analyzeBtn.disabled = false;
            } else {
                analyzeBtn.disabled = true;
            }
        }

        function analyzeImages() {
            const s1File = document.getElementById('s1Image').files[0];
            const s2File = document.getElementById('s2Image').files[0];

            if (!s1File || !s2File) {
                showError('Please select both images.');
                return;
            }

            // Basic file type validation (images only)
            const validImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/tiff', 'image/tif'];
            if (!validImageTypes.includes(s1File.type) || !validImageTypes.includes(s2File.type)) {
                showError('Please select valid image files.');
                return;
            }

            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').style.display = 'none';
            document.getElementById('errorMessage').style.display = 'none';

            // Create form data
            const formData = new FormData();
            formData.append('s1_image', s1File);
            formData.append('s2_image', s2File);

            // Send request
            fetch('/analyze', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loading').style.display = 'none';
                
                if (data.error) {
                    showError(data.error);
                } else {
                    showResults(data);
                }
            })
            .catch(error => {
                document.getElementById('loading').style.display = 'none';
                showError('An error occurred: ' + error.message);
            });
        }

        function showResults(data) {
            // Update metrics with proper formatting
            const accuracyValue = data.accuracy ? data.accuracy.toFixed(2) + '%' : 'N/A';
            document.getElementById('accuracyValue').textContent = accuracyValue;

            // Dynamic styling based on accuracy value
            const accuracyCard = document.querySelector('.accuracy-highlight');
            if (data.accuracy) {
                if (data.accuracy >= 90) {
                    // Excellent accuracy - green gradient
                    accuracyCard.style.background = 'linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%)';
                    accuracyCard.style.boxShadow = '0 15px 40px rgba(86, 171, 47, 0.6)';
                } else if (data.accuracy >= 85) {
                    // Good accuracy - blue gradient
                    accuracyCard.style.background = 'linear-gradient(135deg, #00d2ff 0%, #3a7bd5 100%)';
                    accuracyCard.style.boxShadow = '0 15px 40px rgba(0, 210, 255, 0.6)';
                } else {
                    // Lower accuracy - orange gradient
                    accuracyCard.style.background = 'linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%)';
                    accuracyCard.style.boxShadow = '0 15px 40px rgba(255, 154, 86, 0.6)';
                }
            }

            // Update burned area with proper formatting
            const burnedAreaValue = data.burned_area_km2 ? data.burned_area_km2.toFixed(4) + ' km²' : 'N/A';
            document.getElementById('burnedAreaValue').textContent = burnedAreaValue;

            document.getElementById('f1Value').textContent =
                data.f1_score ? data.f1_score.toFixed(4) : 'N/A';
            document.getElementById('precisionValue').textContent =
                data.precision ? data.precision.toFixed(4) : 'N/A';
            document.getElementById('recallValue').textContent =
                data.recall ? data.recall.toFixed(4) : 'N/A';

            // Update images
            if (data.prediction_image) {
                document.getElementById('predictionImage').src = 'data:image/png;base64,' + data.prediction_image;
            }
            if (data.performance_plot) {
                document.getElementById('performancePlot').src = 'data:image/png;base64,' + data.performance_plot;
            }

            // Show results with animation
            const resultsSection = document.getElementById('results');
            resultsSection.style.display = 'block';
            resultsSection.style.opacity = '0';
            resultsSection.style.transform = 'translateY(30px)';

            setTimeout(() => {
                resultsSection.style.transition = 'all 0.6s ease';
                resultsSection.style.opacity = '1';
                resultsSection.style.transform = 'translateY(0)';
            }, 100);
        }

        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
    </script>
</body>
</html>
